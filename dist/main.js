import path2 from 'path';
import { fileURLToPath } from 'url';
import dotenv from 'dotenv';
import fastify from 'fastify';
import cors from '@fastify/cors';
import jwt from 'jsonwebtoken';

// node_modules/.pnpm/tsup@8.5.0_typescript@5.8.3/node_modules/tsup/assets/esm_shims.js
var getFilename = () => fileURLToPath(import.meta.url);
var getDirname = () => path2.dirname(getFilename());
var __dirname = /* @__PURE__ */ getDirname();
var NODE_ENV = typeof process.env.NODE_ENV === "string" ? process.env.NODE_ENV.trim() : "dev";
dotenv.config({ path: path2.resolve(__dirname, "../env/.env") });
switch (NODE_ENV) {
  case "dev":
    dotenv.config({
      path: path2.resolve(__dirname, "../env/.env.development"),
      override: true
    });
    break;
  case "test":
    dotenv.config({
      path: path2.resolve(__dirname, "../env/.env.test"),
      override: true
    });
    break;
  case "prod":
    dotenv.config({
      path: path2.resolve(__dirname, "../env/.env.production"),
      override: true
    });
    break;
}
var authMiddleware = (fastify2) => {
  fastify2.decorateRequest("user_id", -1);
  fastify2.addHook("onRequest", (request, reply, done) => {
    const skipRoutes = process.env.SKIP_ROUTES ?? [];
    const url = new URL(`https://localhost:18889${request.url}`);
    const pathname = url.pathname;
    if (skipRoutes.includes(pathname) || pathname.includes("trpc")) {
      return done();
    }
    let token = "";
    const authorization = request.headers.authorization;
    if (authorization) {
      const regexp = /^Bearer\s+(.+)/.exec(authorization);
      if (regexp) {
        token = regexp[1].trim();
      }
    }
    if (!token) {
      return reply.status(401).send({ message: "Unauthorized" });
    }
    let jwtPayload;
    try {
      jwtPayload = jwt.verify(token, process.env.AUTH_SECRET);
    } catch (error) {
      return reply.status(401).send({ message: "Unauthorized" });
    }
    const user_id = jwtPayload.user_id;
    if (!user_id) {
      return reply.status(401).send({ message: "Unauthorized" });
    }
    request.user_id = user_id;
    return done();
  });
};

// src/middleware/err.middleware.ts
var errMiddleware = (fastify2) => {
  fastify2.setErrorHandler((error, request, reply) => {
    return reply.status(500).send({ message: error.message ?? "Internal Server Error" });
  });
};

// src/router/src/recording/douyin/douyin.ts
function douyinRouter(app2) {
  app2.post("/recording/douyin", async (req, reply) => {
    reply.send({ message: "douyin" });
  });
}

// src/router/index.ts
var router_default = (app2) => {
  app2.register(douyinRouter, { prefix: "/api" });
};

// src/main.ts
var app = fastify();
app.register(cors, { origin: "*" });
router_default(app);
authMiddleware(app);
errMiddleware(app);
(async () => {
  try {
    const listen = await app.listen({
      port: +process.env.PORT,
      host: "0.0.0.0"
    });
    console.log(`\u670D\u52A1\u5DF2\u5F00\u542F: ${listen}`);
  } catch (error) {
    console.log(error);
    process.exit(1);
  }
})();
