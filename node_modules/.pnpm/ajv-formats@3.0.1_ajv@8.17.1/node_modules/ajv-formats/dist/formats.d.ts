import type { Format } from "ajv";
export type FormatMode = "fast" | "full";
export type FormatName = "date" | "time" | "date-time" | "iso-time" | "iso-date-time" | "duration" | "uri" | "uri-reference" | "uri-template" | "url" | "email" | "hostname" | "ipv4" | "ipv6" | "regex" | "uuid" | "json-pointer" | "json-pointer-uri-fragment" | "relative-json-pointer" | "byte" | "int32" | "int64" | "float" | "double" | "password" | "binary";
export type DefinedFormats = {
    [key in FormatName]: Format;
};
export declare const fullFormats: DefinedFormats;
export declare const fastFormats: DefinedFormats;
export declare const formatNames: FormatName[];
