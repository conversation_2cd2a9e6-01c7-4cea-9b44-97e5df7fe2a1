{"version": 3, "file": "formats.js", "sourceRoot": "", "sources": ["../src/formats.ts"], "names": [], "mappings": ";;;AAqCA,SAAS,MAAM,CACb,QAA0C,EAC1C,OAA8B;IAE9B,OAAO,EAAC,QAAQ,EAAE,OAAO,EAAC,CAAA;AAC5B,CAAC;AAEY,QAAA,WAAW,GAAmB;IACzC,uDAAuD;IACvD,IAAI,EAAE,MAAM,CAAC,IAAI,EAAE,WAAW,CAAC;IAC/B,4DAA4D;IAC5D,IAAI,EAAE,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE,WAAW,CAAC;IACxC,WAAW,EAAE,MAAM,CAAC,WAAW,CAAC,IAAI,CAAC,EAAE,eAAe,CAAC;IACvD,UAAU,EAAE,MAAM,CAAC,OAAO,EAAE,EAAE,cAAc,CAAC;IAC7C,eAAe,EAAE,MAAM,CAAC,WAAW,EAAE,EAAE,kBAAkB,CAAC;IAC1D,2DAA2D;IAC3D,QAAQ,EAAE,wEAAwE;IAClF,GAAG;IACH,eAAe,EACb,woCAAwoC;IAC1oC,oDAAoD;IACpD,cAAc,EACZ,mLAAmL;IACrL,yDAAyD;IACzD,0DAA0D;IAC1D,GAAG,EAAE,odAAod;IACzd,KAAK,EACH,0IAA0I;IAC5I,QAAQ,EACN,uGAAuG;IACzG,mHAAmH;IACnH,IAAI,EAAE,mFAAmF;IACzF,IAAI,EAAE,k/BAAk/B;IACx/B,KAAK;IACL,2CAA2C;IAC3C,IAAI,EAAE,8DAA8D;IACpE,oDAAoD;IACpD,+DAA+D;IAC/D,cAAc,EAAE,2BAA2B;IAC3C,2BAA2B,EAAE,8DAA8D;IAC3F,wFAAwF;IACxF,uBAAuB,EAAE,kDAAkD;IAC3E,+GAA+G;IAC/G,gDAAgD;IAChD,IAAI;IACJ,wBAAwB;IACxB,KAAK,EAAE,EAAC,IAAI,EAAE,QAAQ,EAAE,QAAQ,EAAE,aAAa,EAAC;IAChD,wBAAwB;IACxB,KAAK,EAAE,EAAC,IAAI,EAAE,QAAQ,EAAE,QAAQ,EAAE,aAAa,EAAC;IAChD,eAAe;IACf,KAAK,EAAE,EAAC,IAAI,EAAE,QAAQ,EAAE,QAAQ,EAAE,cAAc,EAAC;IACjD,gBAAgB;IAChB,MAAM,EAAE,EAAC,IAAI,EAAE,QAAQ,EAAE,QAAQ,EAAE,cAAc,EAAC;IAClD,uCAAuC;IACvC,QAAQ,EAAE,IAAI;IACd,2BAA2B;IAC3B,MAAM,EAAE,IAAI;CACb,CAAA;AAEY,QAAA,WAAW,GAAmB;IACzC,GAAG,mBAAW;IACd,IAAI,EAAE,MAAM,CAAC,4BAA4B,EAAE,WAAW,CAAC;IACvD,IAAI,EAAE,MAAM,CACV,4EAA4E,EAC5E,WAAW,CACZ;IACD,WAAW,EAAE,MAAM,CACjB,qGAAqG,EACrG,eAAe,CAChB;IACD,UAAU,EAAE,MAAM,CAChB,6EAA6E,EAC7E,cAAc,CACf;IACD,eAAe,EAAE,MAAM,CACrB,0GAA0G,EAC1G,kBAAkB,CACnB;IACD,4EAA4E;IAC5E,GAAG,EAAE,4CAA4C;IACjD,eAAe,EAAE,yEAAyE;IAC1F,uCAAuC;IACvC,mHAAmH;IACnH,6FAA6F;IAC7F,KAAK,EACH,kHAAkH;CACrH,CAAA;AAEY,QAAA,WAAW,GAAG,MAAM,CAAC,IAAI,CAAC,mBAAW,CAAiB,CAAA;AAEnE,SAAS,UAAU,CAAC,IAAY;IAC9B,iDAAiD;IACjD,OAAO,IAAI,GAAG,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,GAAG,GAAG,KAAK,CAAC,IAAI,IAAI,GAAG,GAAG,KAAK,CAAC,CAAC,CAAA;AACjE,CAAC;AAED,MAAM,IAAI,GAAG,4BAA4B,CAAA;AACzC,MAAM,IAAI,GAAG,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAAA;AAEhE,SAAS,IAAI,CAAC,GAAW;IACvB,gEAAgE;IAChE,MAAM,OAAO,GAAoB,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,CAAA;IAC/C,IAAI,CAAC,OAAO;QAAE,OAAO,KAAK,CAAA;IAC1B,MAAM,IAAI,GAAW,CAAC,OAAO,CAAC,CAAC,CAAC,CAAA;IAChC,MAAM,KAAK,GAAW,CAAC,OAAO,CAAC,CAAC,CAAC,CAAA;IACjC,MAAM,GAAG,GAAW,CAAC,OAAO,CAAC,CAAC,CAAC,CAAA;IAC/B,OAAO,CACL,KAAK,IAAI,CAAC;QACV,KAAK,IAAI,EAAE;QACX,GAAG,IAAI,CAAC;QACR,GAAG,IAAI,CAAC,KAAK,KAAK,CAAC,IAAI,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAC5D,CAAA;AACH,CAAC;AAED,SAAS,WAAW,CAAC,EAAU,EAAE,EAAU;IACzC,IAAI,CAAC,CAAC,EAAE,IAAI,EAAE,CAAC;QAAE,OAAO,SAAS,CAAA;IACjC,IAAI,EAAE,GAAG,EAAE;QAAE,OAAO,CAAC,CAAA;IACrB,IAAI,EAAE,GAAG,EAAE;QAAE,OAAO,CAAC,CAAC,CAAA;IACtB,OAAO,CAAC,CAAA;AACV,CAAC;AAED,MAAM,IAAI,GAAG,iEAAiE,CAAA;AAE9E,SAAS,OAAO,CAAC,cAAwB;IACvC,OAAO,SAAS,IAAI,CAAC,GAAW;QAC9B,MAAM,OAAO,GAAoB,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,CAAA;QAC/C,IAAI,CAAC,OAAO;YAAE,OAAO,KAAK,CAAA;QAC1B,MAAM,EAAE,GAAW,CAAC,OAAO,CAAC,CAAC,CAAC,CAAA;QAC9B,MAAM,GAAG,GAAW,CAAC,OAAO,CAAC,CAAC,CAAC,CAAA;QAC/B,MAAM,GAAG,GAAW,CAAC,OAAO,CAAC,CAAC,CAAC,CAAA;QAC/B,MAAM,EAAE,GAAuB,OAAO,CAAC,CAAC,CAAC,CAAA;QACzC,MAAM,MAAM,GAAW,OAAO,CAAC,CAAC,CAAC,KAAK,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAA;QAClD,MAAM,GAAG,GAAW,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAA;QACtC,MAAM,GAAG,GAAW,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAA;QACtC,IAAI,GAAG,GAAG,EAAE,IAAI,GAAG,GAAG,EAAE,IAAI,CAAC,cAAc,IAAI,CAAC,EAAE,CAAC;YAAE,OAAO,KAAK,CAAA;QACjE,IAAI,EAAE,IAAI,EAAE,IAAI,GAAG,IAAI,EAAE,IAAI,GAAG,GAAG,EAAE;YAAE,OAAO,IAAI,CAAA;QAClD,cAAc;QACd,MAAM,MAAM,GAAG,GAAG,GAAG,GAAG,GAAG,MAAM,CAAA;QACjC,MAAM,KAAK,GAAG,EAAE,GAAG,GAAG,GAAG,MAAM,GAAG,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAA;QACtD,OAAO,CAAC,KAAK,KAAK,EAAE,IAAI,KAAK,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,KAAK,EAAE,IAAI,MAAM,KAAK,CAAC,CAAC,CAAC,IAAI,GAAG,GAAG,EAAE,CAAA;IACvF,CAAC,CAAA;AACH,CAAC;AAED,SAAS,WAAW,CAAC,EAAU,EAAE,EAAU;IACzC,IAAI,CAAC,CAAC,EAAE,IAAI,EAAE,CAAC;QAAE,OAAO,SAAS,CAAA;IACjC,MAAM,EAAE,GAAG,IAAI,IAAI,CAAC,aAAa,GAAG,EAAE,CAAC,CAAC,OAAO,EAAE,CAAA;IACjD,MAAM,EAAE,GAAG,IAAI,IAAI,CAAC,aAAa,GAAG,EAAE,CAAC,CAAC,OAAO,EAAE,CAAA;IACjD,IAAI,CAAC,CAAC,EAAE,IAAI,EAAE,CAAC;QAAE,OAAO,SAAS,CAAA;IACjC,OAAO,EAAE,GAAG,EAAE,CAAA;AAChB,CAAC;AAED,SAAS,cAAc,CAAC,EAAU,EAAE,EAAU;IAC5C,IAAI,CAAC,CAAC,EAAE,IAAI,EAAE,CAAC;QAAE,OAAO,SAAS,CAAA;IACjC,MAAM,EAAE,GAAG,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAA;IACxB,MAAM,EAAE,GAAG,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAA;IACxB,IAAI,CAAC,CAAC,EAAE,IAAI,EAAE,CAAC;QAAE,OAAO,SAAS,CAAA;IACjC,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,CAAA;IAC1B,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,CAAA;IAC1B,IAAI,EAAE,GAAG,EAAE;QAAE,OAAO,CAAC,CAAA;IACrB,IAAI,EAAE,GAAG,EAAE;QAAE,OAAO,CAAC,CAAC,CAAA;IACtB,OAAO,CAAC,CAAA;AACV,CAAC;AAED,MAAM,mBAAmB,GAAG,OAAO,CAAA;AACnC,SAAS,WAAW,CAAC,cAAwB;IAC3C,MAAM,IAAI,GAAG,OAAO,CAAC,cAAc,CAAC,CAAA;IAEpC,OAAO,SAAS,SAAS,CAAC,GAAW;QACnC,iDAAiD;QACjD,MAAM,QAAQ,GAAa,GAAG,CAAC,KAAK,CAAC,mBAAmB,CAAC,CAAA;QACzD,OAAO,QAAQ,CAAC,MAAM,KAAK,CAAC,IAAI,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAA;IACxE,CAAC,CAAA;AACH,CAAC;AAED,SAAS,eAAe,CAAC,GAAW,EAAE,GAAW;IAC/C,IAAI,CAAC,CAAC,GAAG,IAAI,GAAG,CAAC;QAAE,OAAO,SAAS,CAAA;IACnC,MAAM,EAAE,GAAG,IAAI,IAAI,CAAC,GAAG,CAAC,CAAC,OAAO,EAAE,CAAA;IAClC,MAAM,EAAE,GAAG,IAAI,IAAI,CAAC,GAAG,CAAC,CAAC,OAAO,EAAE,CAAA;IAClC,IAAI,CAAC,CAAC,EAAE,IAAI,EAAE,CAAC;QAAE,OAAO,SAAS,CAAA;IACjC,OAAO,EAAE,GAAG,EAAE,CAAA;AAChB,CAAC;AAED,SAAS,kBAAkB,CAAC,GAAW,EAAE,GAAW;IAClD,IAAI,CAAC,CAAC,GAAG,IAAI,GAAG,CAAC;QAAE,OAAO,SAAS,CAAA;IACnC,MAAM,CAAC,EAAE,EAAE,EAAE,CAAC,GAAG,GAAG,CAAC,KAAK,CAAC,mBAAmB,CAAC,CAAA;IAC/C,MAAM,CAAC,EAAE,EAAE,EAAE,CAAC,GAAG,GAAG,CAAC,KAAK,CAAC,mBAAmB,CAAC,CAAA;IAC/C,MAAM,GAAG,GAAG,WAAW,CAAC,EAAE,EAAE,EAAE,CAAC,CAAA;IAC/B,IAAI,GAAG,KAAK,SAAS;QAAE,OAAO,SAAS,CAAA;IACvC,OAAO,GAAG,IAAI,WAAW,CAAC,EAAE,EAAE,EAAE,CAAC,CAAA;AACnC,CAAC;AAED,MAAM,gBAAgB,GAAG,MAAM,CAAA;AAC/B,MAAM,GAAG,GACP,8nCAA8nC,CAAA;AAEhoC,SAAS,GAAG,CAAC,GAAW;IACtB,gGAAgG;IAChG,OAAO,gBAAgB,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,CAAA;AACpD,CAAC;AAED,MAAM,IAAI,GAAG,oEAAoE,CAAA;AAEjF,SAAS,IAAI,CAAC,GAAW;IACvB,IAAI,CAAC,SAAS,GAAG,CAAC,CAAA;IAClB,OAAO,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,CAAA;AACvB,CAAC;AAED,MAAM,SAAS,GAAG,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC,CAAA;AAC5B,MAAM,SAAS,GAAG,CAAC,IAAI,EAAE,GAAG,CAAC,CAAA;AAE7B,SAAS,aAAa,CAAC,KAAa;IAClC,OAAO,MAAM,CAAC,SAAS,CAAC,KAAK,CAAC,IAAI,KAAK,IAAI,SAAS,IAAI,KAAK,IAAI,SAAS,CAAA;AAC5E,CAAC;AAED,SAAS,aAAa,CAAC,KAAa;IAClC,4FAA4F;IAC5F,OAAO,MAAM,CAAC,SAAS,CAAC,KAAK,CAAC,CAAA;AAChC,CAAC;AAED,SAAS,cAAc;IACrB,OAAO,IAAI,CAAA;AACb,CAAC;AAED,MAAM,QAAQ,GAAG,UAAU,CAAA;AAC3B,SAAS,KAAK,CAAC,GAAW;IACxB,IAAI,QAAQ,CAAC,IAAI,CAAC,GAAG,CAAC;QAAE,OAAO,KAAK,CAAA;IACpC,IAAI;QACF,IAAI,MAAM,CAAC,GAAG,CAAC,CAAA;QACf,OAAO,IAAI,CAAA;KACZ;IAAC,OAAO,CAAC,EAAE;QACV,OAAO,KAAK,CAAA;KACb;AACH,CAAC"}