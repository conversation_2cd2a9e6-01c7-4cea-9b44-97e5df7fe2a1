!function(e,n){"object"==typeof exports&&"undefined"!=typeof module?n(exports):"function"==typeof define&&define.amd?define(["exports"],n):n(e.ms={})}(this,(function(e){var n=/^(-?(?:\d+)?\.?\d+) *(m(?:illiseconds?|s(?:ecs?)?))?(s(?:ec(?:onds?|s)?)?)?(m(?:in(?:utes?|s)?)?)?(h(?:ours?|rs?)?)?(d(?:ays?)?)?(w(?:eeks?|ks?)?)?(y(?:ears?|rs?)?)?$/,s=864e5,o=365.25*s;function r(e,n,s,o){var r=(0|e)===e?e:~~(e+.5);return n+r+(o?" "+s+(1!=r?"s":""):s[0])}e.format=function(e,n){var t=e<0?"-":"",u=e<0?-e:e;return u<1e3?e+(n?" ms":"ms"):u<6e4?r(u/1e3,t,"second",n):u<36e5?r(u/6e4,t,"minute",n):u<s?r(u/36e5,t,"hour",n):u<o?r(u/s,t,"day",n):r(u/o,t,"year",n)},e.parse=function(e){var r,t=e.toLowerCase().match(n);if(null!=t&&(r=parseFloat(t[1])))return null!=t[3]?1e3*r:null!=t[4]?6e4*r:null!=t[5]?36e5*r:null!=t[6]?r*s:null!=t[7]?r*s*7:null!=t[8]?r*o:r}}));